section .data                               ; 定义已初始化数据段：所有在此段定义的变量在程序启动时已被赋值，存储在可执行文件中
    ; program strings (message to display)  ; 程序字符串（用于显示的消息）
    msg_title       db '=== Shape Drawing Program ===', 10, 0  ; db指令定义字节数据：'==='为ASCII字符，10为换行符LF的ASCII码，0为字符串结束符null terminator
    msg_menu        db 10, 'Select a shape to draw:', 10       ; 多行字符串定义：10为换行符，创建格式化的菜单显示
                    db '1. Rectangle', 10                      ; 继续定义字符串：每行以换行符结尾，显示菜单选项1
                    db '2. Triangle', 10                       ; 菜单选项2：三角形，db指令逐字节存储ASCII字符
                    db '3. Circle', 10                         ; 菜单选项3：圆形，10为换行符的十进制ASCII码
                    db '4. Square', 10                         ; 菜单选项4：正方形，ASCII字符按顺序存储在内存中
                    db '5. Exit', 10                           ; 菜单选项5：退出程序，每个字符占用1字节内存
                    db 'Enter your choice (1-5): ', 0          ; 提示用户输入，以null字符0结尾标识字符串结束

    ; Rectangle messages                    ; 矩形相关的提示消息
    msg_rect_title  db 10, '--- Rectangle Drawing ---', 10, 0  ; 矩形标题：以换行开始和结束，null结尾的C风格字符串
    msg_width       db 'Enter width (1-80): ', 0               ; 宽度输入提示：null结尾字符串，用于print_string函数
    msg_height      db 'Enter height (1-25): ', 0              ; 高度输入提示：ASCII字符串，0表示字符串结束
    msg_rect_error  db 'Value out of range! Width: 1-80, Height: 1-25.', 10, 0  ; 矩形错误消息：范围验证失败时显示

    ; Triangle messages                     ; 三角形相关的提示消息
    msg_tri_title   db 10, '--- Triangle Drawing ---', 10, 0   ; 三角形标题：格式化显示，前后换行增强可读性
    msg_tri_size    db 'Enter triangle size (1-25): ', 0       ; 三角形大小提示：用户输入验证范围1-25
    msg_tri_error   db 'Value out of range! Size must be 1-25.', 10, 0  ; 三角形错误消息：输入超出有效范围时显示

    ; Circle messages                       ; 圆形相关的提示消息
    msg_circ_title  db 10, '--- Circle Drawing ---', 10, 0     ; 圆形标题：统一的标题格式，便于用户识别当前操作
    msg_circ_info   db 'Drawing a fixed-size circle...', 10, 0 ; 圆形信息：说明圆形大小固定，无需用户输入参数

    ; Square messages                       ; 正方形相关的提示消息
    msg_square_title db 10, '--- Square Drawing ---', 10, 0    ; 正方形标题：与其他形状标题保持一致的格式
    msg_square_size  db 'Enter square size (1-25): ', 0        ; 正方形大小提示：边长范围1-25，用于创建n×n正方形
    msg_square_error db 'Value out of range! Size must be 1-25.', 10, 0  ; 正方形错误消息：输入验证失败的提示

    ; Error and info messages for user input  ; 用户输入相关的错误和信息提示
    msg_error_input db 'Invalid input! Please enter a number.', 10, 0    ; 输入错误：非数字输入时的提示消息
    msg_error_choice db 'Invalid choice! Please enter 1-5.', 10, 0       ; 选择错误：菜单选择超出范围时的提示
    msg_max_attempts db 'Too many invalid attempts. Returning to menu.', 10, 0  ; 最大尝试次数：防止无限循环的安全机制
    msg_goodbye     db 10, 'Thank you for using Shape Drawing Program!', 10, 0  ; 再见消息：程序退出时的友好提示

    ; Drawing characters                    ; 绘图用字符定义
    star            db '*'                  ; 星号字符：ASCII码42，用于绘制形状的填充字符
    space           db ' '                  ; 空格字符：ASCII码32，用于形状中的空白区域
    newline         db 10                   ; 换行字符：ASCII码10(LF)，用于行结束，在Unix/Linux系统中表示新行

    ; Constant value                        ; 常量值定义
    max_attempts    equ 3                   ; equ指令定义常量：编译时替换，不占用内存空间，用于限制输入错误重试次数

section .bss                               ; 定义未初始化数据段：BSS(Block Started by Symbol)段，用于声明未初始化的变量
    ; variables (uninitialized)              ; 未初始化变量：程序启动时由系统自动清零，不占用可执行文件空间
    choice          resd 1                  ; resd指令保留双字(4字节)：存储用户菜单选择(1-5)，32位整数变量
    width           resd 1                  ; 矩形宽度变量：保留4字节内存空间，用于存储1-80范围的宽度值
    height          resd 1                  ; 矩形高度变量：32位无符号整数，存储1-25范围的高度值
    size            resd 1                  ; 三角形/正方形大小：通用尺寸变量，存储1-25范围的大小参数
    input           resb 16                 ; resb指令保留字节：16字节输入缓冲区，用于存储用户键盘输入的字符串
    attempts        resd 1                  ; 尝试次数计数器：4字节整数，记录用户输入错误次数，防止无限重试

section .text                               ; 定义代码段：包含可执行指令，程序的主体逻辑部分
    global _start                           ; global指令：声明_start为全局符号，链接器可见的程序入口点

_start:                                     ; 程序入口标签：Linux系统调用程序的起始执行点，等同于C语言的main函数
    ; Show program title at start          ; 程序启动时显示标题
    mov edi, msg_title                      ; mov指令：将msg_title的内存地址加载到EDI寄存器，EDI常用作目标指针
    call print_string                      ; call指令：调用print_string函数，将返回地址压入栈，跳转到函数执行

main_loop:                                  ; 主循环标签：程序的核心循环，反复显示菜单直到用户选择退出
    ; Display main menu and get choice     ; 显示主菜单并获取用户选择
    mov edi, msg_menu                       ; 将菜单字符串地址装入EDI：传递给print_string函数的参数
    call print_string                      ; 调用字符串打印函数：显示菜单选项到标准输出

    ; Read user choice (integer) and validate  ; 读取用户选择(整数)并验证有效性
    call read_int                           ; 调用整数读取函数：从标准输入读取数字，结果存储在EAX寄存器中
    cmp eax, -1                             ; cmp指令：比较EAX与-1，-1表示输入无效(非数字或读取失败)
    je .invalid_choice                      ; je指令：如果相等(ZF=1)则跳转到无效选择处理代码
    cmp eax, 1                              ; 检查输入是否小于1：菜单选项的最小有效值
    jl .invalid_choice                      ; jl指令：如果小于(SF≠OF)则跳转，处理超出下限的输入
    cmp eax, 5                              ; 检查输入是否大于5：菜单选项的最大有效值
    jg .invalid_choice                      ; jg指令：如果大于(ZF=0且SF=OF)则跳转，处理超出上限的输入

    mov [choice], eax                       ; 存储有效选择：将EAX中的值移动到choice变量，方括号表示内存地址

    ; Jump to appropriate handler          ; 跳转到相应的处理程序
    cmp eax, 1                              ; 比较选择是否为1：矩形绘制选项
    je draw_rectangle_menu                  ; je指令：相等则跳转到矩形菜单处理函数
    cmp eax, 2                              ; 比较选择是否为2：三角形绘制选项
    je draw_triangle_menu                   ; 相等则跳转到三角形菜单处理函数
    cmp eax, 3                              ; 比较选择是否为3：圆形绘制选项
    je draw_circle_menu                     ; 相等则跳转到圆形菜单处理函数
    cmp eax, 4                              ; 比较选择是否为4：正方形绘制选项
    je draw_square_menu                     ; 相等则跳转到正方形菜单处理函数
    cmp eax, 5                              ; 比较选择是否为5：退出程序选项
    je exit_program                         ; 相等则跳转到程序退出处理函数

.invalid_choice:                            ; 本地标签：处理无效菜单选择，点号表示局部标签作用域
    ; handle invalid menu choice           ; 处理无效的菜单选择
    mov edi, msg_error_choice               ; 加载错误消息地址：提示用户输入无效
    call print_string                      ; 显示错误消息：告知用户选择范围为1-5
    jmp main_loop                           ; jmp指令：无条件跳转回主循环，重新显示菜单

; Rectangle drawing menu and function       ; 矩形绘制菜单和函数
draw_rectangle_menu:                        ; 矩形菜单函数标签：处理矩形绘制的用户交互
    mov edi, msg_rect_title                 ; 加载矩形标题消息地址：准备显示矩形绘制提示
    call print_string                      ; 显示矩形标题：让用户知道当前在矩形绘制模式

    ; Initialize attempt counter            ; 初始化尝试计数器
    mov dword [attempts], 0                 ; 将attempts变量设为0：dword指定32位操作，重置错误尝试计数
.get_width:                                 ; 获取宽度的本地标签：宽度输入循环的起始点
    ; If exceeded max attempts -> back to menu  ; 如果超过最大尝试次数则返回主菜单
    cmp dword [attempts], max_attempts      ; 比较当前尝试次数与最大允许次数(3次)
    jge .max_attempts_reached               ; jge指令：大于等于则跳转，防止用户无限次输入错误

    ; Ask width                            ; 询问宽度
    mov edi, msg_width                      ; 加载宽度提示消息地址：显示输入范围1-80
    call print_string                      ; 显示宽度输入提示：引导用户输入有效宽度
    call read_int                           ; 读取用户输入的整数：结果保存在EAX寄存器

    ; Validate width (1-80)                ; 验证宽度范围(1-80)
    cmp eax, -1                             ; 检查是否为无效输入：-1表示非数字输入
    je .invalid_input_width                 ; 如果是无效输入则跳转到错误处理
    cmp eax, 1                              ; 检查宽度是否小于最小值1
    jl .invalid_range_width                 ; 小于1则跳转到范围错误处理
    cmp eax, 80                             ; 检查宽度是否大于最大值80：防止超出终端显示范围
    jg .invalid_range_width                 ; 大于80则跳转到范围错误处理

    mov [width], eax                        ; 存储有效宽度：将EAX中的值保存到width变量
    jmp .get_height                         ; 跳转到获取高度：宽度验证通过，继续获取高度

.invalid_input_width:                       ; 无效宽度输入处理标签：处理非数字输入
    mov edi, msg_error_input                ; 加载输入错误消息：提示用户输入必须是数字
    call print_string                      ; 显示错误消息：告知用户输入格式错误
    inc dword [attempts]                    ; 增加尝试计数：inc指令将attempts变量值加1
    jmp .get_width                          ; 重新获取宽度：返回宽度输入循环

.invalid_range_width:                       ; 宽度范围错误处理标签：处理超出1-80范围的输入
    mov edi, msg_rect_error                 ; 加载矩形错误消息：提示有效范围1-80
    call print_string                      ; 显示范围错误消息：告知用户输入超出有效范围
    inc dword [attempts]                    ; 增加尝试计数：记录错误输入次数
    jmp .get_width                          ; 重新获取宽度：给用户再次输入的机会

; Get height                               ; 获取高度
.get_height:                                ; 获取高度标签：宽度验证通过后开始获取高度参数
    mov dword [attempts], 0                 ; 重置尝试计数器：为高度输入重新开始计数，独立于宽度尝试次数
.get_height_loop:                           ; 高度输入循环标签：允许用户多次尝试输入有效高度
    cmp dword [attempts], max_attempts      ; 检查高度输入尝试次数：防止无限循环的安全机制
    jge .max_attempts_reached               ; 超过最大尝试次数则跳转：返回主菜单避免程序卡死

    mov edi, msg_height                     ; 加载高度提示消息：显示输入范围1-25
    call print_string                      ; 显示高度输入提示：引导用户输入矩形高度
    call read_int                           ; 读取高度输入：从标准输入读取数字到EAX

    ; validate height (1-25)               ; 验证高度范围(1-25)
    cmp eax, -1                             ; 检查输入有效性：-1表示读取失败或非数字输入
    je .invalid_input_height                ; 无效输入则跳转到错误处理：显示格式错误提示
    cmp eax, 1                              ; 检查高度下限：最小高度为1行
    jl .invalid_range_height                ; 小于1则跳转：处理超出下限的输入
    cmp eax, 25                             ; 检查高度上限：最大高度25行，防止超出终端显示
    jg .invalid_range_height                ; 大于25则跳转：处理超出上限的输入

    mov [height], eax                       ; 保存有效高度：将验证通过的高度值存储到变量
    call draw_rectangle                     ; 调用矩形绘制函数：使用width和height绘制矩形
    jmp main_loop                           ; 返回主菜单：绘制完成后回到程序主循环

.invalid_input_height:                      ; 无效高度输入处理：处理非数字或读取失败的情况
    mov edi, msg_error_input                ; 加载输入错误消息：提示用户输入必须是数字
    call print_string                      ; 显示错误提示：告知用户输入格式不正确
    inc dword [attempts]                    ; 增加尝试计数：记录错误输入次数
    jmp .get_height_loop                    ; 返回高度输入循环：给用户重新输入的机会

.invalid_range_height:                      ; 高度范围错误处理：处理超出1-25范围的输入
    mov edi, msg_rect_error                 ; 加载范围错误消息：显示有效范围提示
    call print_string                      ; 显示范围错误：告知用户输入超出有效范围
    inc dword [attempts]                    ; 增加尝试计数：记录范围错误次数
    jmp .get_height_loop                    ; 返回高度输入循环：允许用户重新输入

.max_attempts_reached:                      ; 最大尝试次数到达处理：防止用户无限次错误输入
    mov edi, msg_max_attempts               ; 加载最大尝试消息：提示用户尝试次数过多
    call print_string                      ; 显示尝试超限提示：告知返回主菜单
    jmp main_loop                           ; 返回主菜单：安全退出当前输入流程

; Triangle drawing menu and function        ; 三角形绘制菜单和函数
draw_triangle_menu:                         ; 三角形菜单函数：处理三角形绘制的用户交互和参数输入
    mov edi, msg_tri_title                  ; 加载三角形标题消息：显示当前进入三角形绘制模式
    call print_string                      ; 显示三角形标题：让用户明确当前操作类型

    mov dword [attempts], 0                 ; 初始化尝试计数器：为三角形大小输入设置错误计数起点
.get_size:                                  ; 获取大小标签：三角形大小参数输入循环的入口点
    cmp dword [attempts], max_attempts      ; 检查尝试次数：比较当前错误次数与最大允许次数
    jge .max_attempts_reached               ; 超过限制则跳转：防止用户无限次输入错误

    mov edi, msg_tri_size                   ; 加载大小提示消息：显示三角形大小输入范围1-25
    call print_string                      ; 显示大小输入提示：引导用户输入有效的三角形大小
    call read_int                           ; 读取用户输入：从键盘获取整数并存储到EAX寄存器

    ; validate size (1-25)                 ; 验证大小范围(1-25)
    cmp eax, -1                             ; 检查输入有效性：-1表示输入格式错误或读取失败
    je .invalid_input_size                  ; 无效输入跳转：处理非数字输入的错误情况
    cmp eax, 1                              ; 检查大小下限：三角形最小大小为1（单行单星）
    jl .invalid_range_size                  ; 小于最小值跳转：处理超出下限的输入
    cmp eax, 25                             ; 检查大小上限：最大25行，防止超出终端显示范围
    jg .invalid_range_size                  ; 大于最大值跳转：处理超出上限的输入

    mov [size], eax                         ; 保存有效大小：将通过验证的大小值存储到size变量
    call draw_triangle                      ; 调用三角形绘制函数：使用size参数绘制对应大小的三角形
    jmp main_loop                           ; 返回主菜单：绘制完成后回到程序主循环

.invalid_input_size:                        ; 无效大小输入处理：处理非数字或格式错误的输入
    mov edi, msg_error_input                ; 加载输入错误消息：提示用户必须输入数字
    call print_string                      ; 显示格式错误提示：告知用户输入格式不正确
    inc dword [attempts]                    ; 增加尝试计数：记录当前错误输入次数
    jmp .get_size                           ; 返回大小输入循环：给用户重新输入的机会

.invalid_range_size:                        ; 大小范围错误处理：处理超出1-25范围的输入
    mov edi, msg_tri_error                  ; 加载三角形错误消息：显示有效范围1-25的提示
    call print_string                      ; 显示范围错误提示：告知用户输入超出有效范围
    inc dword [attempts]                    ; 增加尝试计数：记录范围错误的次数
    jmp .get_size                           ; 返回大小输入循环：允许用户重新输入

.max_attempts_reached:                      ; 最大尝试次数处理：当错误次数达到上限时的处理
    mov edi, msg_max_attempts               ; 加载最大尝试消息：提示用户尝试次数过多
    call print_string                      ; 显示尝试超限提示：告知用户将返回主菜单
    jmp main_loop                           ; 返回主菜单：防止无限循环，确保程序健壮性

; Circle drawing menu and function          ; 圆形绘制菜单和函数
draw_circle_menu:                           ; 圆形菜单函数：处理圆形绘制，无需用户输入参数
    mov edi, msg_circ_title                 ; 加载圆形标题消息：显示当前进入圆形绘制模式
    call print_string                      ; 显示圆形标题：让用户知道开始圆形绘制操作
    mov edi, msg_circ_info                  ; 加载圆形信息消息：说明圆形大小固定，无需输入参数
    call print_string                      ; 显示圆形信息：告知用户将绘制固定大小的圆形

    ; Directly call the circle drawing function without input  ; 直接调用圆形绘制函数，无需用户输入
    call draw_circle                       ; 调用圆形绘制函数：使用预定义参数绘制椭圆形状
    jmp main_loop                           ; 返回主菜单：绘制完成后回到程序主循环

; draw ellipse-like circle using x^2 + (2y)^2 = r^2  ; 使用椭圆公式x²+(2y)²=r²绘制类圆形
draw_circle:                                ; 圆形绘制函数：使用数学公式创建圆形的字符艺术
    push ebx                                ; 保存EBX寄存器：函数调用约定要求保存被调用者保存的寄存器
    push ecx                                ; 保存ECX寄存器：确保调用前的寄存器状态不被破坏
    push edx                                ; 保存EDX寄存器：用于计算过程中的临时存储
    push esi                                ; 保存ESI寄存器：虽然本函数未使用，但遵循调用约定
    push edi                                ; 保存EDI寄存器：将用作x坐标计数器

    mov ebp, -15                            ; 初始化y坐标：从-15开始，EBP用作y坐标计数器

y_loop:                                     ; y坐标循环：外层循环，控制垂直方向的绘制
    mov edi, -30                            ; 初始化x坐标：从-30开始，EDI用作x坐标计数器

x_loop:                                     ; x坐标循环：内层循环，控制水平方向的绘制
    ; Calculate x^2                        ; 计算x的平方
    mov eax, edi                            ; 将x坐标值复制到EAX：准备进行平方计算
    imul eax, eax                           ; 计算x²：imul进行有符号整数乘法，EAX = x * x

    ; Calculate (2y)^2                     ; 计算(2y)的平方
    mov ebx, ebp                            ; 将y坐标值复制到EBX：准备计算2y
    imul ebx, 2                             ; 计算2y：将y坐标乘以2，用于椭圆压缩效果
    imul ebx, ebx                           ; 计算(2y)²：EBX = (2y) * (2y)

    add eax, ebx                            ; 计算椭圆方程：EAX = x² + (2y)²

    cmp eax, 900                            ; 比较与半径平方：900 = 30²，判断点是否在椭圆内
    jg .print_space                         ; 如果大于半径平方：点在椭圆外，打印空格

.print_star:                                ; 打印星号标签：当点在椭圆内时执行
    mov eax, 4                              ; sys_write系统调用号：Linux系统调用，用于写入数据
    mov ebx, 1                              ; 文件描述符1：stdout标准输出，向终端显示
    mov ecx, star                           ; 数据源地址：指向星号字符的内存地址
    mov edx, 1                              ; 写入字节数：1个字节，即一个字符
    int 0x80                                ; 调用内核：触发系统调用，执行写入操作
    jmp .continue_x_loop                    ; 跳转继续：跳过空格打印，继续x循环

.print_space:                               ; 打印空格标签：当点在椭圆外时执行
    mov eax, 4                              ; sys_write系统调用：准备写入操作
    mov ebx, 1                              ; stdout文件描述符：输出到终端
    mov ecx, space                          ; 空格字符地址：指向空格字符的内存位置
    mov edx, 1                              ; 字节数1：写入一个空格字符
    int 0x80                                ; 执行系统调用：向终端输出空格

.continue_x_loop:                           ; x循环继续标签：每次x坐标处理后的汇合点
    inc edi                                 ; x坐标递增：移动到下一个水平位置
    cmp edi, 30                             ; 检查x坐标上限：x坐标范围是-30到30
    jle x_loop                              ; 小于等于30继续：如果未达到右边界，继续x循环

    ; Print newline at end of row          ; 在行末打印换行符
    mov eax, 4                              ; sys_write系统调用：准备换行操作
    mov ebx, 1                              ; stdout文件描述符：输出到终端
    mov ecx, newline                        ; 换行符地址：指向换行字符(ASCII 10)
    mov edx, 1                              ; 字节数1：输出一个换行符
    int 0x80                                ; 执行换行：完成当前行，移动到下一行

    inc ebp                                 ; y坐标递增：移动到下一个垂直位置
    cmp ebp, 15                             ; 检查y坐标上限：y坐标范围是-15到15
    jle y_loop                              ; 小于等于15继续：如果未达到下边界，继续y循环

    ; Restore registers                     ; 恢复寄存器状态
    pop edi                                 ; 恢复EDI寄存器：按照LIFO顺序恢复保存的寄存器
    pop esi                                 ; 恢复ESI寄存器：确保调用者的寄存器状态不变
    pop edx                                 ; 恢复EDX寄存器：函数调用约定的要求
    pop ecx                                 ; 恢复ECX寄存器：保持调用前后状态一致
    pop ebx                                 ; 恢复EBX寄存器：最后恢复第一个保存的寄存器
    ret                                     ; 函数返回：弹出返回地址，跳转回调用处

; Square drawing menu and function          ; 正方形绘制菜单和函数
draw_square_menu:                           ; 正方形菜单函数：处理正方形绘制的用户交互和参数输入
    mov edi, msg_square_title               ; 加载正方形标题消息：显示当前进入正方形绘制模式
    call print_string                      ; 显示正方形标题：让用户明确当前操作类型

    mov dword [attempts], 0                 ; 初始化尝试计数器：为正方形大小输入设置错误计数起点
.get_size:                                  ; 获取大小标签：正方形边长参数输入循环的入口点
    cmp dword [attempts], max_attempts      ; 检查尝试次数：比较当前错误次数与最大允许次数
    jge .max_attempts_reached               ; 超过限制则跳转：防止用户无限次输入错误

    mov edi, msg_square_size                ; 加载大小提示消息：显示正方形边长输入范围1-25
    call print_string                      ; 显示大小输入提示：引导用户输入有效的正方形边长
    call read_int                           ; 读取用户输入：从键盘获取整数并存储到EAX寄存器

    ; Validate size (1-25)                 ; 验证大小范围(1-25)
    cmp eax, -1                             ; 检查输入有效性：-1表示输入格式错误或读取失败
    je .invalid_input_size                  ; 无效输入跳转：处理非数字输入的错误情况
    cmp eax, 1                              ; 检查大小下限：正方形最小边长为1（1×1）
    jl .invalid_range_size                  ; 小于最小值跳转：处理超出下限的输入
    cmp eax, 25                             ; 检查大小上限：最大边长25，防止超出终端显示范围
    jg .invalid_range_size                  ; 大于最大值跳转：处理超出上限的输入

    mov [size], eax                         ; 保存有效大小：将通过验证的边长值存储到size变量
    call draw_square                       ; 调用正方形绘制函数：使用size参数绘制对应大小的正方形
    jmp main_loop                           ; 返回主菜单：绘制完成后回到程序主循环

.invalid_input_size:                        ; 无效大小输入处理：处理非数字或格式错误的输入
    mov edi, msg_error_input                ; 加载输入错误消息：提示用户必须输入数字
    call print_string                      ; 显示格式错误提示：告知用户输入格式不正确
    inc dword [attempts]                    ; 增加尝试计数：记录当前错误输入次数
    jmp .get_size                           ; 返回大小输入循环：给用户重新输入的机会

.invalid_range_size:                        ; 大小范围错误处理：处理超出1-25范围的输入
    mov edi, msg_square_error               ; 加载正方形错误消息：显示有效范围1-25的提示
    call print_string                      ; 显示范围错误提示：告知用户输入超出有效范围
    inc dword [attempts]                    ; 增加尝试计数：记录范围错误的次数
    jmp .get_size                           ; 返回大小输入循环：允许用户重新输入

.max_attempts_reached:                      ; 最大尝试次数处理：当错误次数达到上限时的处理
    mov edi, msg_max_attempts               ; 加载最大尝试消息：提示用户尝试次数过多
    call print_string                      ; 显示尝试超限提示：告知用户将返回主菜单
    jmp main_loop                           ; 返回主菜单：防止无限循环，确保程序健壮性


; --- Drawing functions ---                 ; 绘制函数区域

; Draw rectangle                           ; 绘制矩形函数
draw_rectangle:                             ; 矩形绘制函数：根据width和height变量绘制矩形
    push ebx                                ; 保存EBX寄存器：遵循调用约定，保护调用者的寄存器状态
    push ecx                                ; 保存ECX寄存器：ECX将用作循环计数器
    push edx                                ; 保存EDX寄存器：EDX将用于系统调用参数

    mov ecx, [height]                       ; 加载矩形高度：将height变量值复制到ECX作为行计数器
.row_loop:                                  ; 行循环标签：外层循环，控制矩形的垂直方向绘制
    push ecx                                ; 保存行计数器：在内层循环前保存当前行计数，防止被覆盖
    mov ecx, [width]                        ; 加载矩形宽度：将width变量值复制到ECX作为列计数器
.col_loop:                                  ; 列循环标签：内层循环，控制矩形的水平方向绘制
    push ecx                                ; 保存列计数器：在系统调用前保存，因为ECX会被用作参数
    mov eax, 4                              ; sys_write系统调用号：Linux内核调用，用于向文件写入数据
    mov ebx, 1                              ; 文件描述符1：stdout标准输出，向终端显示内容
    mov ecx, star                           ; 数据源地址：指向星号字符的内存地址
    mov edx, 1                              ; 写入字节数：输出1个字节，即一个星号字符
    int 0x80                                ; 触发系统调用：执行写入操作，在终端显示一个星号
    pop ecx                                 ; 恢复列计数器：从栈中恢复ECX的值
    loop .col_loop                          ; loop指令：ECX递减，非零则跳转，实现列循环

    ; print newline                        ; 打印换行符
    mov eax, 4                              ; sys_write系统调用：准备输出换行符
    mov ebx, 1                              ; stdout文件描述符：输出到终端
    mov ecx, newline                        ; 换行符地址：指向ASCII码10的换行字符
    mov edx, 1                              ; 字节数1：输出一个换行符
    int 0x80                                ; 执行换行：完成当前行，光标移动到下一行开始

    pop ecx                                 ; 恢复行计数器：从栈中恢复外层循环的计数器
    loop .row_loop                          ; 行循环控制：ECX递减，非零则继续下一行绘制

    pop edx                                 ; 恢复EDX寄存器：按LIFO顺序恢复保存的寄存器
    pop ecx                                 ; 恢复ECX寄存器：函数调用约定要求
    pop ebx                                 ; 恢复EBX寄存器：确保调用者状态不变
    ret                                     ; 函数返回：弹出返回地址，跳转回调用处

; Draw triangle                            ; 绘制三角形函数
draw_triangle:                              ; 三角形绘制函数：创建递增式三角形，每行星号数递增
    push ebx                                ; 保存EBX寄存器：遵循调用约定，保护调用者的寄存器状态
    push ecx                                ; 保存ECX寄存器：ECX将用作行计数器和列计数器
    push edx                                ; 保存EDX寄存器：EDX将用于系统调用和临时计算

    mov ecx, 1                              ; 初始化行计数：从第1行开始，第一行只有1个星号
    mov ebx, [size]                         ; 加载三角形大小：将size变量复制到EBX作为最大行数

.row_loop:                                  ; 行循环标签：外层循环，控制三角形的行数
    push ecx                                ; 保存当前行计数：在内层循环前保存，防止被覆盖

    ; Print stars for this row             ; 为当前行打印星号
    mov edx, ecx                            ; 复制行号到EDX：当前行的星号数等于行号
.col_loop:                                  ; 列循环标签：内层循环，打印当前行的所有星号
    push edx                                ; 保存星号计数器：在系统调用前保存，防止被覆盖
    mov eax, 4                              ; sys_write系统调用号：Linux内核调用，用于输出字符
    mov ebx, 1                              ; 文件描述符1：stdout标准输出，向终端显示
    mov ecx, star                           ; 星号字符地址：指向星号字符的内存位置
    mov edx, 1                              ; 输出长度1：输出一个字符（星号）
    int 0x80                                ; 触发系统调用：执行写入操作，显示一个星号
    pop edx                                 ; 恢复星号计数器：从栈中恢复EDX的值
    dec edx                                 ; 星号计数递减：减少待打印的星号数量
    jnz .col_loop                           ; 非零则继续：如果还有星号需要打印，继续列循环

    ; Print newline                        ; 打印换行符
    push ecx                                ; 保存ECX：在系统调用中ECX会被修改，需要保护
    mov eax, 4                              ; sys_write系统调用：准备输出换行符
    mov ebx, 1                              ; stdout文件描述符：输出到终端
    mov ecx, newline                        ; 换行符地址：指向换行字符的内存地址
    mov edx, 1                              ; 输出长度1：输出一个换行符
    int 0x80                                ; 执行换行：完成当前行，移动到下一行
    pop ecx                                 ; 恢复ECX：恢复之前保存的值

    pop ecx                                 ; 恢复行计数器：从栈中恢复当前行号
    inc ecx                                 ; 行计数递增：移动到下一行，星号数加1
    cmp ecx, [size]                         ; 比较与最大行数：检查是否已达到三角形的最大行数
    jle .row_loop                           ; 小于等于则继续：如果未完成，继续下一行绘制

    pop edx                                 ; 恢复EDX寄存器：按LIFO顺序恢复保存的寄存器
    pop ecx                                 ; 恢复ECX寄存器：函数调用约定要求
    pop ebx                                 ; 恢复EBX寄存器：确保调用者状态不变
    ret                                     ; 函数返回：弹出返回地址，跳转回调用处

; Draw square (similar to rectangle but equal width & height)  ; 绘制正方形（类似矩形但宽高相等）
draw_square:                                ; 正方形绘制函数：创建n×n的正方形，边长由size变量确定
    push ebx                                ; 保存EBX寄存器：遵循调用约定，保护调用者的寄存器状态
    push ecx                                ; 保存ECX寄存器：ECX将用作行和列的计数器
    push edx                                ; 保存EDX寄存器：EDX将用于系统调用参数

    mov ecx, [size]                         ; 加载正方形大小：将size变量值复制到ECX作为行计数器
.row_loop:                                  ; 行循环标签：外层循环，控制正方形的垂直方向绘制
    push ecx                                ; 保存行计数器：在内层循环前保存当前行计数
    mov ecx, [size]                         ; 重新加载大小：ECX现在用作列计数器，值等于边长

.col_loop:                                  ; 列循环标签：内层循环，控制正方形的水平方向绘制
    ; Print star                           ; 打印星号
    push ecx                                ; 保存列计数器：在系统调用前保存，防止被覆盖
    mov eax, 4                              ; sys_write系统调用号：Linux内核调用，用于输出字符
    mov ebx, 1                              ; 文件描述符1：stdout标准输出，向终端显示
    mov ecx, star                           ; 星号字符地址：指向星号字符的内存位置
    mov edx, 1                              ; 输出长度1：输出一个字符（星号）
    int 0x80                                ; 触发系统调用：执行写入操作，显示一个星号
    pop ecx                                 ; 恢复列计数器：从栈中恢复ECX的值

    dec ecx                                 ; 列计数递减：减少待打印的星号数量
    jnz .col_loop                           ; 非零则继续：如果还有星号需要打印，继续列循环

    ; Print newline after row              ; 行结束后打印换行符
    push ecx                                ; 保存ECX：在系统调用中ECX会被修改，需要保护
    mov eax, 4                              ; sys_write系统调用：准备输出换行符
    mov ebx, 1                              ; stdout文件描述符：输出到终端
    mov ecx, newline                        ; 换行符地址：指向换行字符的内存地址
    mov edx, 1                              ; 输出长度1：输出一个换行符
    int 0x80                                ; 执行换行：完成当前行，移动到下一行
    pop ecx                                 ; 恢复ECX：恢复之前保存的值

    pop ecx                                 ; 恢复行计数器：从栈中恢复外层循环的计数器
    dec ecx                                 ; 行计数递减：减少待绘制的行数
    jnz .row_loop                           ; 非零则继续：如果还有行需要绘制，继续行循环

    pop edx                                 ; 恢复EDX寄存器：按LIFO顺序恢复保存的寄存器
    pop ecx                                 ; 恢复ECX寄存器：函数调用约定要求
    pop ebx                                 ; 恢复EBX寄存器：确保调用者状态不变
    ret                                     ; 函数返回：弹出返回地址，跳转回调用处


; --- Utility functions ---                ; 工具函数区域

; Print string (null-terminated)           ; 打印字符串（以null结尾）
print_string:                               ; 字符串打印函数：打印以null字符结尾的C风格字符串
    push ebx                                ; 保存EBX寄存器：遵循调用约定，保护调用者状态
    push ecx                                ; 保存ECX寄存器：ECX将用作字符串遍历指针
    push edx                                ; 保存EDX寄存器：EDX将用作长度计数器

    mov ecx, edi                            ; 字符串地址复制：将EDI中的字符串地址复制到ECX
    mov edx, 0                              ; 初始化长度计数器：EDX清零，准备计算字符串长度
.strlen:                                    ; 字符串长度计算标签：遍历字符串计算长度
    cmp byte [ecx], 0                       ; 检查当前字符：比较当前字节是否为null结束符
    je .print                               ; 如果是null则跳转：字符串结束，开始打印
    inc ecx                                 ; 指针递增：移动到下一个字符
    inc edx                                 ; 长度递增：字符串长度加1
    jmp .strlen                             ; 继续遍历：回到字符串长度计算循环
.print:                                     ; 打印标签：执行实际的字符串输出操作
    mov eax, 4                              ; sys_write系统调用号：Linux内核写入调用
    mov ebx, 1                              ; 文件描述符1：stdout标准输出流
    mov ecx, edi                            ; 字符串起始地址：恢复原始字符串地址用于输出
    int 0x80                                ; 触发系统调用：EDX已包含字符串长度，执行输出

    pop edx                                 ; 恢复EDX寄存器：按LIFO顺序恢复保存的寄存器
    pop ecx                                 ; 恢复ECX寄存器：函数调用约定要求
    pop ebx                                 ; 恢复EBX寄存器：确保调用者状态不变
    ret                                     ; 函数返回：弹出返回地址，跳转回调用处

; Read integer from input                   ; 从输入中读取整数
read_int:                                   ; 整数读取函数：从标准输入读取字符串并转换为整数
    push ebx                                ; 保存EBX寄存器：遵循调用约定，保护调用者状态
    push ecx                                ; 保存ECX寄存器：ECX将用作缓冲区指针和循环计数器
    push edx                                ; 保存EDX寄存器：EDX将用于系统调用参数
    push esi                                ; 保存ESI寄存器：确保调用者寄存器状态完整

    ; Clear input buffer first              ; 首先清空输入缓冲区
    mov edi, input                          ; 缓冲区地址：将input缓冲区地址加载到EDI
    mov ecx, 16                             ; 缓冲区大小：16字节的缓冲区长度
    xor al, al                              ; 清零AL寄存器：将要填充的字节值设为0
    rep stosb                               ; 重复存储字节：将缓冲区所有字节设为0，清空缓冲区

    ; Read input                           ; 读取输入
    mov eax, 3                              ; sys_read系统调用号：Linux内核读取调用
    mov ebx, 0                              ; 文件描述符0：stdin标准输入流
    mov ecx, input                          ; 缓冲区地址：数据读取的目标缓冲区
    mov edx, 16                             ; 最大读取长度：最多读取16字节
    int 0x80                                ; 触发系统调用：EAX将包含实际读取的字节数

    ; Check if read failed or no bytes read  ; 检查读取是否失败或无字节读取
    cmp eax, 0                              ; 比较读取字节数：检查是否读取失败（返回值≤0）
    jle .invalid                            ; 小于等于0跳转：读取失败或无数据，标记为无效

    ; Check for empty input                ; 检查空输入
    cmp eax, 1                              ; 比较字节数为1：仅包含换行符的情况
    je .invalid                             ; 相等则跳转：只有换行符，无有效数字输入

    ; Replace newline with null terminator ; 用null结束符替换换行符
    dec eax                                 ; 字节数减1：去除最后的换行符
    mov byte [input + eax], 0               ; 设置null结束符：在字符串末尾添加0，创建C风格字符串

    ; Convert string to integer            ; 将字符串转换为整数
    xor eax, eax                            ; 结果清零：EAX将存储转换后的整数结果
    xor ecx, ecx                            ; 索引清零：ECX用作字符串索引计数器

.convert_loop:                              ; 转换循环标签：逐字符处理字符串转换为整数
    ; load one byte from input buffer at position ECX into EBX (zero-extended)  ; 从输入缓冲区ECX位置加载一个字节到EBX（零扩展）
    movzx ebx, byte [input + ecx]           ; 获取字符：零扩展加载，将8位字符扩展为32位

    ; test if character is 0 (null terminator) -> end of string  ; 测试字符是否为0（null结束符）-> 字符串结束
    test bl, bl                             ; 检查null结束符：测试字符是否为0
    jz .done                                ; 如果为零则跳转：字符串结束，转换完成

    ; Check if character is a digit        ; 检查字符是否为数字
    cmp bl, '0'                             ; 与ASCII '0'比较：检查字符是否在数字范围内
    jb .invalid                             ; 小于'0'则跳转：非数字字符，输入无效
    cmp bl, '9'                             ; 与ASCII '9'比较：检查字符上限
    ja .invalid                             ; 大于'9'则跳转：非数字字符，输入无效

    ; Convert ASCII character to digit and add to result  ; 将ASCII字符转换为数字并加到结果中
    sub bl, '0'                             ; ASCII转数字：'0'-'9' -> 0-9的数值转换
    imul eax, 10                            ; 结果乘以10：为新数字腾出个位空间
    add eax, ebx                            ; 结果加数字：将当前数字加到累积结果中

    inc ecx                                 ; 移动到下一字符：缓冲区索引递增
    jmp .convert_loop                       ; 重复下一字符：继续转换循环

.invalid:                                   ; 无效输入标签：处理格式错误或非法字符
    ; if invalid character detected, seat EAX = -1 to signal error  ; 如果检测到无效字符，设置EAX = -1以发出错误信号
    mov eax, -1                             ; 返回-1表示无效输入：约定的错误返回值

.done:                                      ; 完成标签：转换成功完成的出口
    ; restore registers saved earlier before returning  ; 在返回前恢复之前保存的寄存器
    pop esi                                 ; 恢复ESI寄存器：按LIFO顺序恢复
    pop edx                                 ; 恢复EDX寄存器：函数调用约定要求
    pop ecx                                 ; 恢复ECX寄存器：确保调用者状态不变
    pop ebx                                 ; 恢复EBX寄存器：最后恢复第一个保存的寄存器
    ret                                     ; 返回调用者：函数执行完成，返回转换结果

exit_program:                               ; 退出程序标签：程序正常退出的处理函数
    ; Print goodbye message                ; 打印再见消息
    mov edi, msg_goodbye                    ; 加载再见消息地址：将友好的退出消息地址装入EDI
    call print_string                      ; 调用字符串打印函数：显示感谢用户的消息

    mov eax, 1                              ; sys_exit系统调用号：Linux内核程序终止调用
    xor ebx, ebx                            ; 退出码0：XOR操作将EBX清零，表示程序正常退出
    int 0x80                                ; 调用内核终止程序：触发系统调用，结束进程执行
